'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Memory, MemoryCategory } from '@/lib/settingsService';
import memoryService from '@/lib/memoryService';
import { getUserConversations, Conversation } from '@/lib/conversationService';

interface MemoryManagerProps {
  selectedChatId?: string; // Para memórias específicas de chat
}

const BACKGROUND_COLORS = [
  '#3B82F6', // blue-500
  '#10B981', // emerald-500
  '#F59E0B', // amber-500
  '#EF4444', // red-500
  '#8B5CF6', // violet-500
  '#06B6D4', // cyan-500
  '#84CC16', // lime-500
  '#F97316', // orange-500
  '#EC4899', // pink-500
  '#6B7280', // gray-500
];

export default function MemoryManager({ selectedChatId }: MemoryManagerProps) {
  const { user } = useAuth();
  const [memories, setMemories] = useState<Memory[]>([]);
  const [categories, setCategories] = useState<MemoryCategory[]>([]);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [showMemoryModal, setShowMemoryModal] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [editingMemory, setEditingMemory] = useState<Memory | null>(null);
  const [editingCategory, setEditingCategory] = useState<MemoryCategory | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Form states
  const [memoryForm, setMemoryForm] = useState({
    title: '',
    content: '',
    backgroundColor: BACKGROUND_COLORS[0],
    isActive: true,
    categoryId: '',
    chatId: selectedChatId || ''
  });

  const [categoryForm, setCategoryForm] = useState({
    name: '',
    description: '',
    backgroundColor: BACKGROUND_COLORS[0],
    isActive: true
  });

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const [memoriesData, categoriesData, conversationsData] = await Promise.all([
        memoryService.getMemories(user.uid),
        memoryService.getCategories(user.uid),
        getUserConversations(user.uid)
      ]);

      setMemories(memoriesData);
      setCategories(categoriesData);
      setConversations(conversationsData);
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateMemory = async () => {
    if (!user || !memoryForm.title.trim() || !memoryForm.content.trim()) return;

    try {
      const memoryData: any = {
        title: memoryForm.title.trim(),
        content: memoryForm.content.trim(),
        backgroundColor: memoryForm.backgroundColor,
        isActive: memoryForm.isActive
      };

      // Adicionar campos opcionais apenas se tiverem valor
      if (memoryForm.categoryId && memoryForm.categoryId.trim()) {
        memoryData.categoryId = memoryForm.categoryId.trim();
      }
      if (memoryForm.chatId && memoryForm.chatId.trim()) {
        memoryData.chatId = memoryForm.chatId.trim();
      }

      await memoryService.createMemory(user.uid, memoryData);

      setMemoryForm({
        title: '',
        content: '',
        backgroundColor: BACKGROUND_COLORS[0],
        isActive: true,
        categoryId: '',
        chatId: selectedChatId || ''
      });
      setShowMemoryModal(false);
      await loadData();
    } catch (error) {
      console.error('Erro ao criar memória:', error);
    }
  };

  const handleUpdateMemory = async () => {
    if (!user || !editingMemory || !memoryForm.title.trim() || !memoryForm.content.trim()) return;

    try {
      const updateData: any = {
        title: memoryForm.title.trim(),
        content: memoryForm.content.trim(),
        backgroundColor: memoryForm.backgroundColor,
        isActive: memoryForm.isActive
      };

      // Adicionar campos opcionais apenas se tiverem valor
      if (memoryForm.categoryId && memoryForm.categoryId.trim()) {
        updateData.categoryId = memoryForm.categoryId.trim();
      }
      if (memoryForm.chatId && memoryForm.chatId.trim()) {
        updateData.chatId = memoryForm.chatId.trim();
      }

      await memoryService.updateMemory(user.uid, editingMemory.id, updateData);

      setEditingMemory(null);
      setShowMemoryModal(false);
      await loadData();
    } catch (error) {
      console.error('Erro ao atualizar memória:', error);
    }
  };

  const handleDeleteMemory = async (memoryId: string) => {
    if (!user || !confirm('Tem certeza que deseja deletar esta memória?')) return;

    try {
      await memoryService.deleteMemory(user.uid, memoryId);
      await loadData();
    } catch (error) {
      console.error('Erro ao deletar memória:', error);
    }
  };

  const handleCreateCategory = async () => {
    if (!user || !categoryForm.name.trim()) return;

    try {
      const categoryData: any = {
        name: categoryForm.name.trim(),
        backgroundColor: categoryForm.backgroundColor,
        isActive: categoryForm.isActive
      };

      // Adicionar descrição apenas se tiver valor
      if (categoryForm.description && categoryForm.description.trim()) {
        categoryData.description = categoryForm.description.trim();
      }

      await memoryService.createCategory(user.uid, categoryData);

      setCategoryForm({
        name: '',
        description: '',
        backgroundColor: BACKGROUND_COLORS[0],
        isActive: true
      });
      setShowCategoryModal(false);
      await loadData();
    } catch (error) {
      console.error('Erro ao criar categoria:', error);
    }
  };

  const handleUpdateCategory = async () => {
    if (!user || !editingCategory || !categoryForm.name.trim()) return;

    try {
      const updateData: any = {
        name: categoryForm.name.trim(),
        backgroundColor: categoryForm.backgroundColor,
        isActive: categoryForm.isActive
      };

      // Adicionar descrição apenas se tiver valor
      if (categoryForm.description && categoryForm.description.trim()) {
        updateData.description = categoryForm.description.trim();
      }

      await memoryService.updateCategory(user.uid, editingCategory.id, updateData);

      setEditingCategory(null);
      setShowCategoryModal(false);
      await loadData();
    } catch (error) {
      console.error('Erro ao atualizar categoria:', error);
    }
  };

  const handleDeleteCategory = async (categoryId: string) => {
    if (!user || !confirm('Tem certeza que deseja deletar esta categoria e todas as suas memórias?')) return;

    try {
      await memoryService.deleteCategory(user.uid, categoryId);
      await loadData();
    } catch (error) {
      console.error('Erro ao deletar categoria:', error);
    }
  };

  const handleToggleCategoryMemories = async (categoryId: string, isActive: boolean) => {
    if (!user) return;

    try {
      await memoryService.toggleCategoryMemories(user.uid, categoryId, isActive);
      await loadData();
    } catch (error) {
      console.error('Erro ao alternar memórias da categoria:', error);
    }
  };

  const openMemoryModal = (memory?: Memory) => {
    if (memory) {
      setEditingMemory(memory);
      setMemoryForm({
        title: memory.title,
        content: memory.content,
        backgroundColor: memory.backgroundColor,
        isActive: memory.isActive,
        categoryId: memory.categoryId || '',
        chatId: memory.chatId || ''
      });
    } else {
      setEditingMemory(null);
      setMemoryForm({
        title: '',
        content: '',
        backgroundColor: BACKGROUND_COLORS[0],
        isActive: true,
        categoryId: '',
        chatId: selectedChatId || ''
      });
    }
    setShowMemoryModal(true);
  };

  const openCategoryModal = (category?: MemoryCategory) => {
    if (category) {
      setEditingCategory(category);
      setCategoryForm({
        name: category.name,
        description: category.description || '',
        backgroundColor: category.backgroundColor,
        isActive: category.isActive
      });
    } else {
      setEditingCategory(null);
      setCategoryForm({
        name: '',
        description: '',
        backgroundColor: BACKGROUND_COLORS[0],
        isActive: true
      });
    }
    setShowCategoryModal(true);
  };

  const filteredMemories = memories.filter(memory => {
    if (selectedCategory === 'all') return true;
    if (selectedCategory === 'uncategorized') return !memory.categoryId;
    return memory.categoryId === selectedCategory;
  });

  if (loading) {
    return (
      <div className="space-y-8 animate-in fade-in duration-500">
        {/* Loading header */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-indigo-600/10 rounded-2xl backdrop-blur-xl border border-blue-500/20"></div>
          <div className="relative p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-xl border border-blue-400/30 backdrop-blur-sm">
                <div className="w-8 h-8 bg-blue-400/30 rounded animate-pulse"></div>
              </div>
              <div>
                <div className="h-8 w-48 bg-gradient-to-r from-blue-400/30 to-purple-400/30 rounded-lg animate-pulse"></div>
                <div className="h-4 w-64 bg-blue-300/20 rounded mt-2 animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Loading content */}
        <div className="flex items-center justify-center py-20">
          <div className="relative">
            {/* Outer ring */}
            <div className="w-16 h-16 border-4 border-blue-500/20 rounded-full animate-spin">
              <div className="absolute top-0 left-0 w-4 h-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
            </div>

            {/* Inner ring */}
            <div className="absolute inset-2 w-12 h-12 border-4 border-purple-500/20 rounded-full animate-spin" style={{ animationDirection: 'reverse' }}>
              <div className="absolute top-0 left-0 w-3 h-3 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full"></div>
            </div>

            {/* Center dot */}
            <div className="absolute inset-6 w-4 h-4 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse"></div>
          </div>
        </div>

        <div className="text-center">
          <h3 className="text-xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-2">
            Carregando memórias...
          </h3>
          <p className="text-blue-300/70">Preparando seu sistema de memórias personalizado</p>
        </div>

        {/* Loading skeleton cards */}
        <div className="grid gap-6 lg:grid-cols-2">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-gradient-to-br from-blue-900/20 via-purple-900/10 to-indigo-900/20 backdrop-blur-xl border border-blue-500/20 rounded-2xl p-6 animate-pulse">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-4 h-4 bg-blue-400/30 rounded-full"></div>
                <div className="h-6 w-32 bg-blue-400/30 rounded-lg"></div>
              </div>
              <div className="space-y-2 mb-4">
                <div className="h-4 w-full bg-blue-300/20 rounded"></div>
                <div className="h-4 w-3/4 bg-blue-300/20 rounded"></div>
              </div>
              <div className="flex justify-between items-center">
                <div className="h-3 w-24 bg-blue-300/20 rounded"></div>
                <div className="flex gap-2">
                  <div className="w-8 h-8 bg-blue-400/30 rounded-lg"></div>
                  <div className="w-8 h-8 bg-blue-400/30 rounded-lg"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 animate-in fade-in duration-500">
      {/* Header aprimorado com estatísticas */}
      <div className="relative">
        {/* Background com gradiente e efeito glass */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-indigo-600/10 rounded-2xl backdrop-blur-xl border border-blue-500/20"></div>

        <div className="relative p-6">
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
            {/* Título e descrição */}
            <div className="flex items-center gap-4">
              <div className="p-3 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-xl border border-blue-400/30 backdrop-blur-sm">
                <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div>
                <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-indigo-400 bg-clip-text text-transparent">
                  Gerenciar Memórias
                </h3>
                <p className="text-blue-300/70 text-sm mt-1">
                  {selectedChatId ? 'Memórias específicas para este chat' : 'Memórias globais e organizadas por categorias'}
                </p>
              </div>
            </div>

            {/* Estatísticas rápidas */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{memories.length}</div>
                  <div className="text-xs text-blue-300/70">Memórias</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">{categories.length}</div>
                  <div className="text-xs text-purple-300/70">Categorias</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{memories.filter(m => m.isActive).length}</div>
                  <div className="text-xs text-green-300/70">Ativas</div>
                </div>
              </div>
            </div>
          </div>

          {/* Botões de ação aprimorados */}
          <div className="flex flex-wrap gap-3 mt-6">
            <button
              onClick={() => openCategoryModal()}
              className="group relative px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-purple-500/25 flex items-center gap-3"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-purple-600/20 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <svg className="w-5 h-5 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              <span className="relative z-10 font-medium">Nova Categoria</span>
            </button>

            <button
              onClick={() => openMemoryModal()}
              className="group relative px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 text-white rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-blue-500/25 flex items-center gap-3"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <svg className="w-5 h-5 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              <span className="relative z-10 font-medium">Nova Memória</span>
            </button>
          </div>
        </div>
      </div>

      {/* Modal de Memória aprimorado */}
      {showMemoryModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-in fade-in duration-300">
          <div className="relative w-full max-w-2xl max-h-[90vh] overflow-hidden animate-in zoom-in-95 duration-300">
            {/* Background com gradiente e glass effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-900/90 via-purple-900/80 to-indigo-900/90 backdrop-blur-2xl rounded-2xl"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-indigo-600/10 rounded-2xl"></div>
            <div className="absolute inset-0 border border-blue-500/30 rounded-2xl"></div>

            <div className="relative p-8 overflow-y-auto max-h-[90vh]">
              {/* Header do modal */}
              <div className="flex justify-between items-center mb-8">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-xl border border-blue-400/30">
                    <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                      {editingMemory ? 'Editar Memória' : 'Nova Memória'}
                    </h3>
                    <p className="text-blue-300/70 text-sm mt-1">
                      {editingMemory ? 'Modifique as informações da memória' : 'Crie uma nova memória para personalizar as respostas da IA'}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setShowMemoryModal(false)}
                  className="p-2 bg-red-500/20 hover:bg-red-500/30 text-red-400 hover:text-red-300 rounded-xl transition-all duration-300 border border-red-500/30"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-6">
              {/* Título */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Título *
                </label>
                <input
                  type="text"
                  value={memoryForm.title}
                  onChange={(e) => setMemoryForm(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white placeholder-blue-400/50 focus:outline-none focus:border-blue-500"
                  placeholder="Ex: Preferências do usuário"
                />
              </div>

              {/* Conteúdo */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Conteúdo *
                </label>
                <textarea
                  value={memoryForm.content}
                  onChange={(e) => setMemoryForm(prev => ({ ...prev, content: e.target.value }))}
                  rows={4}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white placeholder-blue-400/50 focus:outline-none focus:border-blue-500 resize-none"
                  placeholder="Ex: O usuário prefere respostas diretas e objetivas..."
                />
              </div>

              {/* Categoria */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Categoria
                </label>
                <select
                  value={memoryForm.categoryId}
                  onChange={(e) => setMemoryForm(prev => ({ ...prev, categoryId: e.target.value }))}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white focus:outline-none focus:border-blue-500"
                >
                  <option value="">Sem categoria</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Chat específico */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Chat específico
                </label>
                <select
                  value={memoryForm.chatId}
                  onChange={(e) => setMemoryForm(prev => ({ ...prev, chatId: e.target.value }))}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white focus:outline-none focus:border-blue-500"
                >
                  <option value="">Memória global (todos os chats)</option>
                  {conversations.map(conversation => (
                    <option key={conversation.id} value={conversation.id}>
                      {conversation.name}
                    </option>
                  ))}
                </select>
                <p className="text-blue-400/70 text-xs mt-1">
                  Se selecionado, esta memória só será usada no chat especificado
                </p>
              </div>

              {/* Cor de fundo */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Cor de fundo
                </label>
                <div className="flex flex-wrap gap-2">
                  {BACKGROUND_COLORS.map(color => (
                    <button
                      key={color}
                      onClick={() => setMemoryForm(prev => ({ ...prev, backgroundColor: color }))}
                      className={`w-8 h-8 rounded-lg border-2 transition-all ${
                        memoryForm.backgroundColor === color
                          ? 'border-white scale-110'
                          : 'border-transparent hover:scale-105'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>

              {/* Status ativo */}
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="memory-active"
                  checked={memoryForm.isActive}
                  onChange={(e) => setMemoryForm(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="w-4 h-4 text-blue-600 bg-blue-900/30 border-blue-700/30 rounded focus:ring-blue-500"
                />
                <label htmlFor="memory-active" className="text-sm text-blue-300">
                  Memória ativa
                </label>
              </div>
              </div>

              <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => setShowMemoryModal(false)}
                className="px-4 py-2 text-blue-300 hover:text-blue-200 hover:bg-blue-800/20 rounded-lg transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={editingMemory ? handleUpdateMemory : handleCreateMemory}
                disabled={!memoryForm.title.trim() || !memoryForm.content.trim()}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                {editingMemory ? 'Atualizar' : 'Criar'}
              </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Categoria */}
      {showCategoryModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-blue-950 border border-blue-700/30 rounded-lg p-6 w-full max-w-lg mx-4">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-medium text-white">
                {editingCategory ? 'Editar Categoria' : 'Nova Categoria'}
              </h3>
              <button
                onClick={() => setShowCategoryModal(false)}
                className="text-blue-300 hover:text-blue-200"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              {/* Nome */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Nome *
                </label>
                <input
                  type="text"
                  value={categoryForm.name}
                  onChange={(e) => setCategoryForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white placeholder-blue-400/50 focus:outline-none focus:border-blue-500"
                  placeholder="Ex: Preferências pessoais"
                />
              </div>

              {/* Descrição */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Descrição
                </label>
                <textarea
                  value={categoryForm.description}
                  onChange={(e) => setCategoryForm(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg text-white placeholder-blue-400/50 focus:outline-none focus:border-blue-500 resize-none"
                  placeholder="Descrição opcional da categoria"
                />
              </div>

              {/* Cor de fundo */}
              <div>
                <label className="block text-sm font-medium text-blue-300 mb-2">
                  Cor de fundo
                </label>
                <div className="flex flex-wrap gap-2">
                  {BACKGROUND_COLORS.map(color => (
                    <button
                      key={color}
                      onClick={() => setCategoryForm(prev => ({ ...prev, backgroundColor: color }))}
                      className={`w-8 h-8 rounded-lg border-2 transition-all ${
                        categoryForm.backgroundColor === color
                          ? 'border-white scale-110'
                          : 'border-transparent hover:scale-105'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>

              {/* Status ativo */}
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="category-active"
                  checked={categoryForm.isActive}
                  onChange={(e) => setCategoryForm(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="w-4 h-4 text-blue-600 bg-blue-900/30 border-blue-700/30 rounded focus:ring-blue-500"
                />
                <label htmlFor="category-active" className="text-sm text-blue-300">
                  Categoria ativa
                </label>
              </div>
            </div>

            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => setShowCategoryModal(false)}
                className="px-4 py-2 text-blue-300 hover:text-blue-200 hover:bg-blue-800/20 rounded-lg transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={editingCategory ? handleUpdateCategory : handleCreateCategory}
                disabled={!categoryForm.name.trim()}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                {editingCategory ? 'Atualizar' : 'Criar'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Sistema de filtros aprimorado */}
      <div className="bg-gradient-to-r from-blue-900/20 via-purple-900/20 to-indigo-900/20 backdrop-blur-xl border border-blue-500/20 rounded-2xl p-6">
        <div className="flex items-center gap-3 mb-4">
          <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
          </svg>
          <h4 className="text-lg font-semibold text-white">Filtros</h4>
        </div>

        <div className="flex flex-wrap gap-3">
          {/* Filtro "Todas" */}
          <button
            onClick={() => setSelectedCategory('all')}
            className={`group relative px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 transform hover:scale-105 flex items-center gap-2 ${
              selectedCategory === 'all'
                ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg shadow-blue-500/25'
                : 'bg-blue-900/30 text-blue-300 hover:bg-blue-800/40 border border-blue-700/30'
            }`}
          >
            {selectedCategory === 'all' && (
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-xl blur opacity-75"></div>
            )}
            <svg className="w-4 h-4 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <span className="relative z-10">Todas</span>
            <span className="relative z-10 px-2 py-0.5 bg-white/20 rounded-full text-xs">{memories.length}</span>
          </button>

          {/* Filtro "Sem categoria" */}
          <button
            onClick={() => setSelectedCategory('uncategorized')}
            className={`group relative px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 transform hover:scale-105 flex items-center gap-2 ${
              selectedCategory === 'uncategorized'
                ? 'bg-gradient-to-r from-gray-600 to-gray-700 text-white shadow-lg shadow-gray-500/25'
                : 'bg-blue-900/30 text-blue-300 hover:bg-blue-800/40 border border-blue-700/30'
            }`}
          >
            {selectedCategory === 'uncategorized' && (
              <div className="absolute inset-0 bg-gradient-to-r from-gray-400/20 to-gray-600/20 rounded-xl blur opacity-75"></div>
            )}
            <svg className="w-4 h-4 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span className="relative z-10">Sem categoria</span>
            <span className="relative z-10 px-2 py-0.5 bg-white/20 rounded-full text-xs">
              {memories.filter(m => !m.categoryId).length}
            </span>
          </button>

          {/* Filtros por categoria */}
          {categories.map(category => {
            const categoryMemories = memories.filter(m => m.categoryId === category.id);
            return (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`group relative px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 transform hover:scale-105 flex items-center gap-2 ${
                  selectedCategory === category.id
                    ? 'text-white shadow-lg'
                    : 'bg-blue-900/30 text-blue-300 hover:bg-blue-800/40 border border-blue-700/30'
                }`}
                style={selectedCategory === category.id ? {
                  background: `linear-gradient(135deg, ${category.backgroundColor}CC, ${category.backgroundColor}AA)`
                } : {}}
              >
                {selectedCategory === category.id && (
                  <div
                    className="absolute inset-0 rounded-xl blur opacity-75"
                    style={{
                      background: `linear-gradient(135deg, ${category.backgroundColor}40, ${category.backgroundColor}20)`
                    }}
                  ></div>
                )}
                <div
                  className="w-3 h-3 rounded-full relative z-10 ring-2 ring-white/30"
                  style={{ backgroundColor: category.backgroundColor }}
                />
                <span className="relative z-10">{category.name}</span>
                <span className="relative z-10 px-2 py-0.5 bg-white/20 rounded-full text-xs">
                  {categoryMemories.length}
                </span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Lista de categorias aprimorada */}
      {categories.length > 0 && (
        <div className="space-y-6">
          <div className="flex items-center gap-3">
            <svg className="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <h4 className="text-xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Categorias
            </h4>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {categories.map(category => {
              const categoryMemories = memories.filter(m => m.categoryId === category.id);
              const activeMemories = categoryMemories.filter(m => m.isActive);

              return (
                <div
                  key={category.id}
                  className="group memory-card relative overflow-hidden rounded-2xl"
                >
                  {/* Background com gradiente baseado na cor da categoria */}
                  <div
                    className="absolute inset-0 opacity-10 group-hover:opacity-20 transition-opacity duration-300"
                    style={{
                      background: `linear-gradient(135deg, ${category.backgroundColor}40, ${category.backgroundColor}20)`
                    }}
                  ></div>

                  {/* Borda com glow effect */}
                  <div
                    className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                    style={{
                      background: `linear-gradient(135deg, ${category.backgroundColor}60, ${category.backgroundColor}30)`,
                      padding: '1px'
                    }}
                  ></div>

                  <div className="relative bg-gradient-to-br from-blue-900/30 via-purple-900/20 to-indigo-900/30 backdrop-blur-xl border border-blue-500/20 rounded-2xl p-6">
                    {/* Header da categoria */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div
                          className="w-5 h-5 rounded-full ring-2 ring-white/30 shadow-lg"
                          style={{ backgroundColor: category.backgroundColor }}
                        />
                        <div>
                          <h5 className="font-bold text-white text-lg">{category.name}</h5>
                          {category.description && (
                            <p className="text-blue-300/70 text-sm mt-1">{category.description}</p>
                          )}
                        </div>
                      </div>

                      {/* Status badge */}
                      <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                        category.isActive
                          ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                          : 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
                      }`}>
                        {category.isActive ? 'Ativa' : 'Inativa'}
                      </div>
                    </div>

                    {/* Estatísticas */}
                    <div className="grid grid-cols-2 gap-4 mb-6">
                      <div className="text-center p-3 bg-blue-500/10 rounded-xl border border-blue-500/20">
                        <div className="text-2xl font-bold text-blue-400">{categoryMemories.length}</div>
                        <div className="text-xs text-blue-300/70">Total</div>
                      </div>
                      <div className="text-center p-3 bg-green-500/10 rounded-xl border border-green-500/20">
                        <div className="text-2xl font-bold text-green-400">{activeMemories.length}</div>
                        <div className="text-xs text-green-300/70">Ativas</div>
                      </div>
                    </div>

                    {/* Botões de ação */}
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleToggleCategoryMemories(category.id, !category.isActive)}
                        className={`flex-1 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                          category.isActive
                            ? 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 text-white shadow-lg shadow-green-500/25'
                            : 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 text-white shadow-lg shadow-gray-500/25'
                        }`}
                      >
                        {category.isActive ? 'Desativar' : 'Ativar'}
                      </button>

                      <button
                        onClick={() => openCategoryModal(category)}
                        className="p-2 bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 hover:text-blue-300 rounded-xl transition-all duration-300 border border-blue-500/30"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>

                      <button
                        onClick={() => handleDeleteCategory(category.id)}
                        className="p-2 bg-red-500/20 hover:bg-red-500/30 text-red-400 hover:text-red-300 rounded-xl transition-all duration-300 border border-red-500/30"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Lista de memórias aprimorada */}
      <div className="space-y-6">
        <div className="flex items-center gap-3">
          <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h4 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
            Memórias {selectedCategory !== 'all' && `(${filteredMemories.length})`}
          </h4>
        </div>

        {filteredMemories.length === 0 ? (
          <div className="relative">
            {/* Background com gradiente */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-indigo-600/5 rounded-2xl backdrop-blur-xl"></div>

            <div className="relative text-center py-16 px-8 border border-blue-500/20 rounded-2xl">
              <div className="mb-6">
                <div className="w-20 h-20 mx-auto bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-2xl flex items-center justify-center border border-blue-400/30">
                  <svg className="w-10 h-10 text-blue-400/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Nenhuma memória encontrada</h3>
              <p className="text-blue-300/70 mb-6">
                {selectedCategory === 'all'
                  ? 'Comece criando sua primeira memória para personalizar as respostas da IA'
                  : 'Não há memórias nesta categoria. Tente outro filtro ou crie uma nova memória.'
                }
              </p>
              <button
                onClick={() => openMemoryModal()}
                className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 text-white rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg shadow-blue-500/25 font-medium"
              >
                Criar primeira memória
              </button>
            </div>
          </div>
        ) : (
          <div className="grid gap-6 lg:grid-cols-2">
            {filteredMemories.map(memory => {
              const category = categories.find(c => c.id === memory.categoryId);
              const chat = conversations.find(c => c.id === memory.chatId);

              return (
                <div
                  key={memory.id}
                  className="group memory-card relative overflow-hidden rounded-2xl"
                >
                  {/* Background com gradiente baseado na cor da memória */}
                  <div
                    className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300"
                    style={{
                      background: `linear-gradient(135deg, ${memory.backgroundColor}60, ${memory.backgroundColor}30)`
                    }}
                  ></div>

                  {/* Borda com glow effect */}
                  <div
                    className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                    style={{
                      background: `linear-gradient(135deg, ${memory.backgroundColor}40, ${memory.backgroundColor}20)`,
                      padding: '1px'
                    }}
                  ></div>

                  <div
                    className="relative backdrop-blur-xl border rounded-2xl p-6"
                    style={{
                      backgroundColor: `${memory.backgroundColor}08`,
                      borderColor: `${memory.backgroundColor}30`
                    }}
                  >
                    {/* Header da memória */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3 flex-1">
                        <div
                          className="w-4 h-4 rounded-full ring-2 ring-white/20 shadow-lg flex-shrink-0"
                          style={{ backgroundColor: memory.backgroundColor }}
                        />
                        <h5 className="font-bold text-white text-lg truncate">{memory.title}</h5>
                      </div>

                      {/* Status badge */}
                      <div className={`px-3 py-1 rounded-full text-xs font-medium flex-shrink-0 ${
                        memory.isActive
                          ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                          : 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
                      }`}>
                        {memory.isActive ? 'Ativa' : 'Inativa'}
                      </div>
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {memory.chatId && chat && (
                        <span className="px-3 py-1 bg-gradient-to-r from-purple-600/20 to-pink-600/20 text-purple-300 text-xs rounded-full border border-purple-500/30 flex items-center gap-1">
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                          {chat.name}
                        </span>
                      )}
                      {memory.chatId && !chat && (
                        <span className="px-3 py-1 bg-gradient-to-r from-red-600/20 to-red-700/20 text-red-300 text-xs rounded-full border border-red-500/30 flex items-center gap-1">
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                          </svg>
                          Chat não encontrado
                        </span>
                      )}
                      {category && (
                        <span
                          className="px-3 py-1 text-xs rounded-full border flex items-center gap-1"
                          style={{
                            backgroundColor: `${category.backgroundColor}20`,
                            color: category.backgroundColor,
                            borderColor: `${category.backgroundColor}40`
                          }}
                        >
                          <div
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: category.backgroundColor }}
                          />
                          {category.name}
                        </span>
                      )}
                    </div>

                    {/* Conteúdo */}
                    <div className="mb-4">
                      <p className="text-blue-100/90 text-sm leading-relaxed line-clamp-3">
                        {memory.content}
                      </p>
                    </div>

                    {/* Footer */}
                    <div className="flex items-center justify-between pt-4 border-t border-white/10">
                      <p className="text-blue-300/50 text-xs">
                        Criado em {new Date(memory.createdAt).toLocaleDateString('pt-BR')}
                      </p>

                      {/* Botões de ação */}
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => memoryService.updateMemory(user!.uid, memory.id, { isActive: !memory.isActive }).then(loadData)}
                          className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-300 ${
                            memory.isActive
                              ? 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 text-white shadow-lg shadow-green-500/25'
                              : 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 text-white shadow-lg shadow-gray-500/25'
                          }`}
                        >
                          {memory.isActive ? 'Desativar' : 'Ativar'}
                        </button>

                        <button
                          onClick={() => openMemoryModal(memory)}
                          className="p-2 bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 hover:text-blue-300 rounded-lg transition-all duration-300 border border-blue-500/30"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>

                        <button
                          onClick={() => handleDeleteMemory(memory.id)}
                          className="p-2 bg-red-500/20 hover:bg-red-500/30 text-red-400 hover:text-red-300 rounded-lg transition-all duration-300 border border-red-500/30"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
